package convert_items

import (
	"context"
	"net/http"
	"showfer-web/repository"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ConvertItemsMongoAPI handles convert items API endpoints for MongoDB
type ConvertItemsMongoAPI struct {
	repo *repository.MongoConvertItemsRepository
}

// NewConvertItemsMongoAPI creates a new ConvertItemsMongoAPI
func NewConvertItemsMongoAPI() *ConvertItemsMongoAPI {
	return &ConvertItemsMongoAPI{
		repo: repository.NewMongoConvertItemsRepository(),
	}
}

// GetBuckets retrieves all available buckets
func (api *ConvertItemsMongoAPI) GetBuckets(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	buckets, err := api.repo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"buckets": buckets})
}

// GetItemsByBucket handles GET /api/v1/convert-items/bucket/:bucketId
func (api *ConvertItemsMongoAPI) GetItemsByBucket(c *gin.Context) {
	bucketID := c.Param("bucketId")
	if bucketID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket ID is required"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := api.repo.GetConvertItemsByBucket(ctx, bucketID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// BucketNameRequest represents the request body for bucket name operations
type BucketNameRequest struct {
	BucketName string `json:"bucketName" binding:"required"`
}

// GetItemsByBucketName handles POST /api/v1/convert-items/by-bucket-name
func (api *ConvertItemsMongoAPI) GetItemsByBucketName(c *gin.Context) {
	var req BucketNameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := api.repo.GetConvertItemsByBucketName(ctx, req.BucketName, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetAllItems retrieves all convert items without bucket filtering
func (api *ConvertItemsMongoAPI) GetAllItems(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := api.repo.GetAllConvertItems(ctx, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetItemByID retrieves a single convert item by ID
func (api *ConvertItemsMongoAPI) GetItemByID(c *gin.Context) {
	itemID := c.Param("id")
	if itemID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Item ID is required"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	item, err := api.repo.GetConvertItemByID(ctx, itemID)
	if err != nil {
		if err.Error() == "convert item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Convert item not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// SearchItems searches convert items by query with optional bucket filtering
func (api *ConvertItemsMongoAPI) SearchItems(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	bucketID := c.Query("bucket_id") // Optional bucket filter

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := api.repo.SearchConvertItems(ctx, query, bucketID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetItemsByBucketAndLocation handles GET /api/v1/convert-items/bucket/:bucketId/location
func (api *ConvertItemsMongoAPI) GetItemsByBucketAndLocation(c *gin.Context) {
	bucketID := c.Param("bucketId")
	if bucketID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket ID is required"})
		return
	}

	location := c.Query("location")
	if location == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Location is required"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Convert bucket ID to ObjectID
	objectID, err := primitive.ObjectIDFromHex(bucketID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID"})
		return
	}

	items, err := api.repo.GetItemsByBucketAndLocation(ctx, objectID, location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, items)
}

// GetSubfoldersByBucket handles GET /api/v1/convert-items/bucket/:bucketId/subfolders
func (api *ConvertItemsMongoAPI) GetSubfoldersByBucket(c *gin.Context) {
	bucketID := c.Param("bucketId")
	if bucketID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket ID is required"})
		return
	}

	location := c.Query("location")
	if location == "" {
		location = "/" // Default to root if not specified
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Convert bucket ID to ObjectID
	objectID, err := primitive.ObjectIDFromHex(bucketID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID"})
		return
	}

	subfolders, err := api.repo.GetSubfoldersByBucket(ctx, objectID, location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, subfolders)
}

// TestConnection handles GET /api/v1/convert-items/test
func (api *ConvertItemsMongoAPI) TestConnection(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Test by getting buckets
	buckets, err := api.repo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to connect to MongoDB",
			"details": err.Error(),
		})
		return
	}

	// Test by getting a few convert items
	result, err := api.repo.GetAllConvertItems(ctx, 1, 5)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get convert items from MongoDB",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":             "MongoDB connection successful",
		"buckets_count":       len(buckets),
		"convert_items_total": result.Total,
		"timestamp":           time.Now(),
	})
}
