import api from './axios';

export interface ConvertItemMongo {
  id: string;
  filename: string;
  location: string;
  duration: number;
  status: string;
  size: number;
  storage_type: string;
  created_at: string;
  updated_at: string;
  c_location: string;
  name: string;
  description: string;
  episode: string;
  width: number;
  height: number;
  fps: number;
  video_codec: string;
  audio_codec: string;
  bitrate: number;
  recorder_id: string;
  codec_settings_version: number;
}

// Get convert items by bucket ID
export const getConvertItemsByBucket = async (bucketId: string): Promise<ConvertItemMongo[]> => {
  const response = await api.get<ConvertItemsListResponse>(`/convert-items/bucket/${bucketId}`);
  return response.data.items;
};

// Response interface for paginated convert items
interface ConvertItemsListResponse {
  items: ConvertItemMongo[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get convert items by bucket name
export const getConvertItemsByBucketName = async (bucketName: string): Promise<ConvertItemMongo[]> => {
  const response = await api.post<ConvertItemsListResponse>('/convert-items/by-bucket-name', {
    bucketName: bucketName
  });
  return response.data.items;
};

// Get convert items by bucket ID and location
export const getConvertItemsByBucketAndLocation = async (
  bucketId: string,
  location: string
): Promise<ConvertItemMongo[]> => {
  const response = await api.post<ConvertItemMongo[]>(
    `/convert-items/by-bucket-and-location`,
    {
      bucketId: bucketId,
      location: location
    }
  );
  return response.data;
};

// Get folders by bucket ID
export const getFoldersByBucket = async (bucketId: string): Promise<string[]> => {
  const response = await api.get<string[]>(`/convert-items/bucket/${bucketId}/folders`);
  return response.data;
};

// Get subfolders by bucket ID and location
export const getSubfoldersByBucket = async (
  bucketId: string, 
  location: string
): Promise<string[]> => {
  const response = await api.get<string[]>(
    `/convert-items/bucket/${bucketId}/subfolders?location=${encodeURIComponent(location)}`
  );
  return response.data;
};

// Get convert item by ID
export const getConvertItemById = async (id: string): Promise<ConvertItemMongo> => {
  const response = await api.get<ConvertItemMongo>(`/convert-items/${id}`);
  return response.data;
};

// Test MongoDB connection for convert_items
export const testConvertItemsConnection = async (): Promise<any> => {
  const response = await api.get('/convert-items/test');
  return response.data;
};
